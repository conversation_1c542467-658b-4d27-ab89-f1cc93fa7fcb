<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>奖励按钮测试</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/ui.css">
    <link rel="stylesheet" href="css/battle-dialog.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }
        h1, h2 {
            color: white;
            text-align: center;
        }
        .button-demo {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 15px 0;
        }
        .test-log {
            background: rgba(0, 0, 0, 0.3);
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 奖励按钮状态测试</h1>
        
        <div class="test-section">
            <h2>按钮状态演示</h2>
            <div class="button-demo">
                <button id="enabledBtn" class="btn btn-primary">领取奖励</button>
                <button id="disabledBtn" class="btn btn-secondary" disabled>奖励已领取</button>
                <button id="toggleBtn" class="btn btn-warning">切换状态</button>
            </div>
        </div>

        <div class="test-section">
            <h2>战斗结果模拟</h2>
            <div class="battle-result-detailed">
                <div class="result-header">
                    <h4>🏆 战斗胜利</h4>
                </div>
                <div class="result-stats">
                    <div class="stat-row">
                        <span class="stat-label">战斗评价:</span>
                        <span class="stat-value victory">完美胜利</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">获得奖励:</span>
                        <span class="stat-value rewards" id="rewardDisplay">💰100 ⭐50</span>
                    </div>
                </div>
            </div>
            <div class="battle-result-buttons">
                <button id="mockRewardBtn" class="btn btn-primary">领取奖励</button>
                <button id="mockRechallengeBtn" class="btn btn-secondary">重新挑战</button>
            </div>
        </div>

        <div class="test-section">
            <h2>操作日志</h2>
            <div id="testLog" class="test-log">
                测试开始...<br>
            </div>
            <button id="clearLogBtn" class="btn btn-secondary">清空日志</button>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logEl = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logEl.innerHTML += `[${timestamp}] ${message}<br>`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        // 模拟已完成关卡集合
        let completedStages = new Set();
        let currentStageId = 'stage_1';

        // 按钮事件
        document.getElementById('toggleBtn').onclick = function() {
            const enabledBtn = document.getElementById('enabledBtn');
            const disabledBtn = document.getElementById('disabledBtn');
            
            if (enabledBtn.disabled) {
                enabledBtn.disabled = false;
                enabledBtn.textContent = '领取奖励';
                enabledBtn.className = 'btn btn-primary';
                log('启用奖励按钮');
            } else {
                enabledBtn.disabled = true;
                enabledBtn.textContent = '奖励已领取';
                enabledBtn.className = 'btn btn-secondary';
                log('禁用奖励按钮');
            }
        };

        document.getElementById('enabledBtn').onclick = function() {
            if (!this.disabled) {
                log('用户点击了启用的奖励按钮');
                this.disabled = true;
                this.textContent = '奖励已领取';
                this.className = 'btn btn-secondary';
            }
        };

        // 模拟战斗奖励按钮
        document.getElementById('mockRewardBtn').onclick = function() {
            const isFirstVictory = !completedStages.has(currentStageId);
            
            if (isFirstVictory && !this.disabled) {
                log(`首次胜利关卡 ${currentStageId}，领取奖励`);
                
                // 立即更新按钮状态
                this.disabled = true;
                this.textContent = '奖励已领取';
                this.className = 'btn btn-secondary';
                
                // 更新奖励显示
                document.getElementById('rewardDisplay').textContent = '已领取';
                
                // 标记关卡完成
                completedStages.add(currentStageId);
                
                // 模拟重新挑战按钮变为主按钮
                const rechallengeBtn = document.getElementById('mockRechallengeBtn');
                rechallengeBtn.className = 'btn btn-primary';
                
                log('奖励按钮已禁用，关卡标记为完成');
            } else if (!isFirstVictory) {
                log(`关卡 ${currentStageId} 已完成，无法重复领取奖励`);
            }
        };

        document.getElementById('mockRechallengeBtn').onclick = function() {
            log(`重新挑战关卡 ${currentStageId}`);
            
            // 重置为战斗准备状态（模拟）
            setTimeout(() => {
                const isCompleted = completedStages.has(currentStageId);
                updateMockBattleResult(isCompleted);
                log('重新挑战完成，显示战斗结果');
            }, 1000);
        };

        // 更新模拟战斗结果
        function updateMockBattleResult(isCompleted) {
            const rewardBtn = document.getElementById('mockRewardBtn');
            const rewardDisplay = document.getElementById('rewardDisplay');
            const rechallengeBtn = document.getElementById('mockRechallengeBtn');
            
            if (isCompleted) {
                // 已完成关卡：禁用奖励按钮
                rewardBtn.disabled = true;
                rewardBtn.textContent = '奖励已领取';
                rewardBtn.className = 'btn btn-secondary';
                rewardDisplay.textContent = '已领取';
                rechallengeBtn.className = 'btn btn-primary';
                log('显示已完成关卡的战斗结果');
            } else {
                // 首次挑战：启用奖励按钮
                rewardBtn.disabled = false;
                rewardBtn.textContent = '领取奖励';
                rewardBtn.className = 'btn btn-primary';
                rewardDisplay.textContent = '💰100 ⭐50';
                rechallengeBtn.className = 'btn btn-secondary';
                log('显示首次挑战的战斗结果');
            }
        }

        // 清空日志
        document.getElementById('clearLogBtn').onclick = function() {
            document.getElementById('testLog').innerHTML = '日志已清空...<br>';
        };

        // 初始化测试
        log('奖励按钮状态测试已加载');
        log('说明：');
        log('1. 上方演示了不同状态的按钮');
        log('2. 中间模拟了真实的战斗结果界面');
        log('3. 点击"领取奖励"会立即禁用按钮');
        log('4. 重新挑战已完成的关卡不会有奖励');
    </script>
</body>
</html> 