// 王国争霸H5 - 主游戏文件

// UI管理器
class UIManager {
    constructor() {
        this.currentPanel = null;
        this.init();
    }

    init() {
        console.log('UI Manager initialized');

        
        // 监听游戏事件
        GameEvents.on(GAME_EVENTS.GAME_START, this.onGameStart.bind(this));
        GameEvents.on(GAME_EVENTS.GAME_PAUSE, this.onGamePause.bind(this));
        GameEvents.on(GAME_EVENTS.GAME_RESUME, this.onGameResume.bind(this));
        
        // 监听战斗事件
        GameEvents.on(GAME_EVENTS.BATTLE_START, this.onBattleStart.bind(this));
        GameEvents.on(GAME_EVENTS.BATTLE_END, this.onBattleEnd.bind(this));
        GameEvents.on(GAME_EVENTS.BATTLE_WIN, this.onBattleWin.bind(this));
        GameEvents.on(GAME_EVENTS.BATTLE_LOSE, this.onBattleLose.bind(this));
    }
    
    // 游戏事件处理
    onGameStart() {
        console.log('UI: Game started');
    }
    
    onGamePause() {
        console.log('UI: Game paused');
    }
    
    onGameResume() {
        console.log('UI: Game resumed');
    }
    
    // 战斗事件处理
    onBattleStart(data) {
        console.log('UI: Battle started', data);
    }
    
    onBattleEnd(data) {
        console.log('UI: Battle ended', data);
        this.hideBattleScreen();

        // 延迟刷新战斗面板，确保军队状态已更新
        setTimeout(() => {
            if (this.ui && this.ui.currentPanel === 'battle') {
                console.log('刷新战斗面板');
                this.ui.loadBattlePanel();
            }
        }, 300);
    }
    
    onBattleWin(data) {
        console.log('UI: Battle won', data);
        this.showMessage('战斗胜利！', MESSAGE_TYPES.SUCCESS);
    }
    
    onBattleLose(data) {
        console.log('UI: Battle lost', data);
        this.showMessage('战斗失败！', MESSAGE_TYPES.ERROR);
    }

    // 切换面板
    togglePanel(panelName) {
        const panel = Utils.$(`#${panelName}Panel`);
        if (!panel) return;

        if (this.currentPanel === panelName) {
            this.hidePanel(panelName);
        } else {
            this.showPanel(panelName);
        }
    }

    // 显示面板
    showPanel(panelName) {
        // 隐藏当前面板
        if (this.currentPanel) {
            this.hidePanel(this.currentPanel);
        }

        const panel = Utils.$(`#${panelName}Panel`);
        const overlay = Utils.$('#panelOverlay');
        const actionBtn = Utils.$(`#${panelName}Btn`);
        
        if (panel) {
            // 显示遮罩层
            if (overlay) {
                Utils.addClass(overlay, 'show');
            }
            
            // 显示面板
            Utils.removeClass(panel, 'hidden');
            Utils.addClass(panel, 'show');
            this.currentPanel = panelName;
            
            // 激活对应的按钮
            if (actionBtn) {
                Utils.addClass(actionBtn, 'active');
            }
            
            // 加载面板内容
            this.loadPanelContent(panelName);
        }
    }

    // 隐藏面板
    hidePanel(panelName) {
        const panel = Utils.$(`#${panelName}Panel`);
        const overlay = Utils.$('#panelOverlay');
        const actionBtn = Utils.$(`#${panelName}Btn`);
        
        if (panel) {
            // 隐藏面板
            Utils.removeClass(panel, 'show');
            
            // 延迟隐藏元素，等待动画完成
            setTimeout(() => {
                Utils.addClass(panel, 'hidden');
            }, 300);
            
            // 隐藏遮罩层
            if (overlay) {
                Utils.removeClass(overlay, 'show');
            }
            
            // 取消按钮激活状态
            if (actionBtn) {
                Utils.removeClass(actionBtn, 'active');
            }
            
            if (this.currentPanel === panelName) {
                this.currentPanel = null;
            }
        }
    }

    // 显示消息
    showMessage(text, type = MESSAGE_TYPES.INFO) {
        const container = Utils.$('#messageContainer');
        if (!container) return;

        const message = Utils.createElement('div', `message ${type}`);
        Utils.setContent(message, text);
        
        container.appendChild(message);

        // 自动移除消息
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 3000);
    }

    // 显示游戏菜单
    showGameMenu() {
        Utils.show('#gameMenu');
    }

    // 隐藏游戏菜单
    hideGameMenu() {
        Utils.hide('#gameMenu');
    }

    // 切换游戏菜单
    toggleGameMenu() {
        const menu = Utils.$('#gameMenu');
        if (menu) {
            Utils.toggle(menu);
        }
    }

    // 加载面板内容
    loadPanelContent(panelName) {
        switch (panelName) {
            case 'building':
                this.loadBuildingPanel();
                break;
            case 'army':
                this.loadArmyPanel();
                break;
            case 'tech':
                this.loadTechPanel();
                break;
            case 'quest':
                this.loadQuestPanel();
                break;
            case 'battle':
                this.loadBattlePanel();
                break;
        }
    }

    // 加载建筑面板
    loadBuildingPanel() {
        const buildingList = Utils.$('#buildingList');
        if (!buildingList) return;

        buildingList.innerHTML = '';

        // 获取当前分类
        const activeCategory = Utils.$('.category-btn.active');
        const category = activeCategory ? activeCategory.dataset.category : 'resource';

        // 过滤建筑
        const buildings = Object.values(GameData.buildings).filter(
            building => building.category === category
        );

        // 生成建筑项
        for (const building of buildings) {
            const buildingItem = this.createBuildingItem(building);
            buildingList.appendChild(buildingItem);
        }
    }

    // 创建建筑项
    createBuildingItem(buildingData) {
        const item = Utils.createElement('div', 'building-item');

        // 检查是否可建造
        const canBuild = window.Game.player.level >= buildingData.unlockLevel &&
                        window.Game.resources.hasResources(buildingData.cost);

        // 检查具体的限制条件
        const levelLocked = window.Game.player.level < buildingData.unlockLevel;
        const resourceLocked = !window.Game.resources.hasResources(buildingData.cost);

        if (!canBuild) {
            Utils.addClass(item, 'disabled');
        }

        // 生成解锁条件说明
        let unlockInfo = '';
        if (levelLocked) {
            unlockInfo = `<div class="unlock-requirement level-locked">🔒 需要等级 ${buildingData.unlockLevel} (当前: ${window.Game.player.level})</div>`;
        } else if (resourceLocked) {
            unlockInfo = `<div class="unlock-requirement resource-locked">💰 资源不足</div>`;
        } else {
            unlockInfo = `<div class="unlock-requirement unlocked">✅ 可以建造</div>`;
        }

        item.innerHTML = `
            <div class="building-header">
                <span class="building-name">${buildingData.icon} ${buildingData.name}</span>
                <span class="building-cost">${this.formatCost(buildingData.cost)}</span>
            </div>
            <div class="building-desc">${buildingData.description}</div>
            <div class="building-stats">
                ${buildingData.produces ? `产出: ${this.formatProduction(buildingData.produces)}` : ''}
                ${buildingData.buildTime ? `建造时间: ${buildingData.buildTime}秒` : ''}
            </div>
            ${unlockInfo}
        `;

        if (canBuild) {
            item.onclick = (e) => {
                e.preventDefault();
                e.stopPropagation();

                try {
                    console.log('Building item clicked:', buildingData.id);

                    // 检查游戏状态
                    if (!window.Game) {
                        console.error('Game object not available');
                        return;
                    }
                    console.log('Game object exists:', !!window.Game);

                    if (!window.Game.buildings) {
                        console.error('Buildings system not available');
                        return;
                    }
                    console.log('Buildings system exists:', !!window.Game.buildings);
                    console.log('Buildings system type:', typeof window.Game.buildings);
                    console.log('enterBuildMode method exists:', typeof window.Game.buildings.enterBuildMode);

                    // 先进入建造模式
                    console.log('About to call enterBuildMode...');
                    const result = window.Game.buildings.enterBuildMode(buildingData.id);
                    console.log('enterBuildMode call completed, result:', result);
                    console.log('Build mode entered for:', buildingData.id);

                    // 检查建造模式状态
                    console.log('Current buildMode:', window.Game.buildings.buildMode);
                    console.log('Current buildingType:', window.Game.buildings.buildingType);

                    // 延迟关闭面板，确保建造模式已经设置完成
                    setTimeout(() => {
                        this.hidePanel('building');
                        console.log('Building panel hidden');
                    }, 100);
                } catch (error) {
                    console.error('Error in building item click:', error);
                    console.error('Error stack:', error.stack);
                }
            };
        }

        return item;
    }

    // 格式化成本
    formatCost(cost) {
        const parts = [];
        for (const [resource, amount] of Object.entries(cost)) {
            const icon = this.getResourceIcon(resource);
            parts.push(`${icon}${amount}`);
        }
        return parts.join(' ');
    }

    // 格式化产出
    formatProduction(production) {
        const parts = [];
        for (const [resource, amount] of Object.entries(production)) {
            const icon = this.getResourceIcon(resource);
            parts.push(`${icon}${amount}/分钟`);
        }
        return parts.join(' ');
    }

    // 获取资源图标
    getResourceIcon(resource) {
        const icons = {
            gold: '💰',
            wood: '🪵',
            stone: '🪨',
            food: '🌾'
        };
        return icons[resource] || '❓';
    }

    // 加载军队面板
    loadArmyPanel() {
        const armyContent = Utils.$('#armyContent');
        if (!armyContent) return;

        // 获取当前标签
        const activeTab = Utils.$('.army-tabs .tab-btn.active');
        const tab = activeTab ? activeTab.dataset.tab : 'recruit';

        if (tab === 'recruit') {
            this.loadArmyRecruitTab(armyContent);
        } else if (tab === 'manage') {
            this.loadArmyManageTab(armyContent);
        }
    }

    // 加载军队招募标签
    loadArmyRecruitTab(container) {
        container.innerHTML = '';

        const unitList = Utils.createElement('div', 'unit-list');

        for (const [unitId, unitData] of Object.entries(GameData.units)) {
            // 跳过敌人单位（如训练假人）
            if (unitData.isEnemy) continue;

            const canTrain = window.Game.player.level >= unitData.unlockLevel &&
                           window.Game.resources.hasResources(unitData.cost) &&
                           (!unitData.requiredBuilding || window.Game.buildings.getBuildingCount(unitData.requiredBuilding) > 0);

            // 检查具体的限制条件
            const levelLocked = window.Game.player.level < unitData.unlockLevel;
            const resourceLocked = !window.Game.resources.hasResources(unitData.cost);
            const buildingLocked = unitData.requiredBuilding &&
                                 window.Game.buildings.getBuildingCount(unitData.requiredBuilding) === 0;

            const unitItem = Utils.createElement('div', 'unit-item');
            if (!canTrain) {
                Utils.addClass(unitItem, 'disabled');
            }

            // 生成解锁条件说明
            let unlockInfo = '';
            if (levelLocked) {
                unlockInfo = `<div class="unlock-requirement level-locked">🔒 需要等级 ${unitData.unlockLevel} (当前: ${window.Game.player.level})</div>`;
            } else if (buildingLocked) {
                const buildingName = GameData.buildings[unitData.requiredBuilding]?.name || unitData.requiredBuilding;
                unlockInfo = `<div class="unlock-requirement building-locked">🏗️ 需要建造${buildingName}</div>`;
            } else if (resourceLocked) {
                unlockInfo = `<div class="unlock-requirement resource-locked">💰 资源不足</div>`;
            } else {
                unlockInfo = `<div class="unlock-requirement unlocked">✅ 可以训练</div>`;
            }

            unitItem.innerHTML = `
                <div class="unit-info">
                    <div class="unit-name">${unitData.icon} ${unitData.name}</div>
                    <div class="unit-stats">
                        攻击: ${unitData.attack} | 防御: ${unitData.defense} | 生命: ${unitData.health}
                    </div>
                    <div class="unit-cost">${this.formatCost(unitData.cost)}</div>
                    ${unlockInfo}
                </div>
                <div class="unit-actions">
                    <button class="btn btn-primary" ${canTrain ? '' : 'disabled'}>训练</button>
                </div>
            `;

            if (canTrain) {
                const trainBtn = unitItem.querySelector('.btn');
                trainBtn.onclick = () => {
                    window.Game.army.trainUnit(unitId);
                    this.loadArmyPanel(); // 刷新面板
                };
            }

            unitList.appendChild(unitItem);
        }

        container.appendChild(unitList);
    }

    // 加载军队管理标签
    loadArmyManageTab(container) {
        container.innerHTML = '';

        const units = window.Game.army.getAllUnits();

        if (units.length === 0) {
            container.innerHTML = '<p>暂无军队</p>';
            return;
        }

        const unitList = Utils.createElement('div', 'unit-list');

        for (const unit of units) {
            const unitItem = Utils.createElement('div', 'unit-item');

            unitItem.innerHTML = `
                <div class="unit-info">
                    <div class="unit-name">${unit.data.icon} ${unit.data.name}</div>
                    <div class="unit-stats">
                        ${unit.isTraining ? `训练中... ${Math.ceil(unit.trainTimeRemaining / 1000)}秒` :
                          `攻击: ${unit.getAttack()} | 防御: ${unit.getDefense()} | 生命: ${unit.health}/${unit.maxHealth}`}
                    </div>
                </div>
                <div class="unit-actions">
                    ${!unit.isTraining ? '<button class="btn btn-secondary">解散</button>' : ''}
                </div>
            `;

            if (!unit.isTraining) {
                const dismissBtn = unitItem.querySelector('.btn');
                if (dismissBtn) {
                    dismissBtn.onclick = () => {
                        window.Game.army.dismissUnit(unit.id);
                        this.loadArmyPanel(); // 刷新面板
                    };
                }
            }

            unitList.appendChild(unitItem);
        }

        container.appendChild(unitList);
    }

    // 加载科技面板
    loadTechPanel() {
        const techTree = Utils.$('#techTree');
        if (!techTree) return;

        techTree.innerHTML = '';

        const techs = window.Game.tech.getAllTechs();

        for (const tech of techs) {
            const canResearch = window.Game.tech.canResearch(tech.id);
            const techItem = Utils.createElement('div', 'tech-item');

            if (tech.isResearched) {
                Utils.addClass(techItem, 'researched');
            } else if (!canResearch) {
                Utils.addClass(techItem, 'disabled');
            }

            // 生成解锁条件说明
            let unlockInfo = '';
            if (tech.isResearched) {
                unlockInfo = `<div class="unlock-requirement researched">🎓 已研发完成</div>`;
            } else if (tech.isResearching) {
                unlockInfo = `<div class="unlock-requirement researching">🔬 研发中...</div>`;
            } else {
                // 检查具体的限制条件
                const levelLocked = window.Game.player.level < tech.data.unlockLevel;
                const resourceLocked = !window.Game.resources.hasResources(tech.data.cost);
                const academyRequired = window.Game.buildings.getBuildingCount('academy') === 0;

                if (levelLocked) {
                    unlockInfo = `<div class="unlock-requirement level-locked">🔒 需要等级 ${tech.data.unlockLevel} (当前: ${window.Game.player.level})</div>`;
                } else if (academyRequired) {
                    unlockInfo = `<div class="unlock-requirement building-locked">🏛️ 需要建造研究院</div>`;
                } else if (resourceLocked) {
                    unlockInfo = `<div class="unlock-requirement resource-locked">💰 资源不足</div>`;
                } else {
                    unlockInfo = `<div class="unlock-requirement unlocked">✅ 可以研发</div>`;
                }
            }

            techItem.innerHTML = `
                <div class="tech-name">${tech.data.name}</div>
                <div class="tech-desc">${tech.data.description}</div>
                <div class="tech-cost">${this.formatCost(tech.data.cost)}</div>
                ${tech.isResearching ? `<div class="tech-progress">研发中... ${Math.ceil(tech.researchTimeRemaining / 1000)}秒</div>` : ''}
                ${unlockInfo}
            `;

            if (canResearch && !tech.isResearching) {
                techItem.onclick = () => {
                    window.Game.tech.researchTech(tech.id);
                    this.loadTechPanel(); // 刷新面板
                };
            }

            techTree.appendChild(techItem);
        }
    }

    // 加载任务面板
    loadQuestPanel() {
        const questContent = Utils.$('#questContent');
        if (!questContent) return;

        // 获取当前标签
        const activeTab = Utils.$('.quest-tabs .tab-btn.active');
        const tab = activeTab ? activeTab.dataset.tab : 'main';

        let quests = [];
        if (tab === 'main') {
            quests = window.Game.quests.getMainQuests();
        } else if (tab === 'daily') {
            quests = window.Game.quests.getDailyQuests();
        } else if (tab === 'achievement') {
            quests = window.Game.quests.getCompletedQuests();
        }

        questContent.innerHTML = '';

        if (quests.length === 0) {
            questContent.innerHTML = '<p>暂无任务</p>';
            return;
        }

        const questList = Utils.createElement('div', 'quest-list');

        for (const quest of quests) {
            const questItem = Utils.createElement('div', 'quest-item');

            if (quest.isCompleted) {
                Utils.addClass(questItem, 'completed');
            }

            questItem.innerHTML = `
                <div class="quest-header">
                    <div class="quest-name">${quest.name}</div>
                    <div class="quest-reward">${this.formatRewards(quest.rewards)}</div>
                </div>
                <div class="quest-desc">${quest.description}</div>
                <div class="quest-progress">
                    进度: ${quest.progress}/${quest.count} (${Math.floor(quest.getProgressPercentage())}%)
                </div>
            `;

            questList.appendChild(questItem);
        }

        questContent.appendChild(questList);
    }

    // 加载战斗面板
    loadBattlePanel() {
        const battleContent = Utils.$('#battleContent');
        if (!battleContent) return;

        // 获取当前模式
        const activeMode = Utils.$('.battle-modes .mode-btn.active');
        const mode = activeMode ? activeMode.dataset.mode : 'campaign';

        if (mode === 'campaign') {
            this.loadCampaignBattles(battleContent);
        }
    }

    // 加载关卡挑战
    loadCampaignBattles(container) {
        container.innerHTML = '';

        // 获取所有关卡
        const allStages = GameData.battles.campaign;

        if (allStages.length === 0) {
            container.innerHTML = '<p>暂无战斗关卡</p>';
            return;
        }

        const battleList = Utils.createElement('div', 'battle-list');

        for (const stage of allStages) {
            const isUnlocked = window.Game.player.level >= stage.unlockLevel;
            const isCompleted = window.Game.battle && window.Game.battle.isStageCompleted(stage.id);
            const battleItem = Utils.createElement('div', 'battle-item');

            // 检查军队状态（除了新手训练）
            let hasArmy = true;
            let armyInfo = '';
            if (stage.id !== 'stage_0') {
                const availableUnits = window.Game.army.getAvailableUnits();
                const trainingUnits = window.Game.army.getAllUnits().filter(unit => unit.isTraining);

                hasArmy = availableUnits.length > 0;

                if (availableUnits.length > 0) {
                    armyInfo = `<div class="army-status ready">🛡️ 可用军队: ${availableUnits.length}个</div>`;
                } else if (trainingUnits.length > 0) {
                    armyInfo = `<div class="army-status training">⏳ 训练中: ${trainingUnits.length}个</div>`;
                } else {
                    armyInfo = `<div class="army-status none">❌ 无可用军队</div>`;
                }
            }

            const canChallenge = isUnlocked && (stage.id === 'stage_0' || hasArmy);

            if (!canChallenge) {
                Utils.addClass(battleItem, 'disabled');
            }

            // 生成解锁条件说明
            let unlockInfo = '';
            if (!isUnlocked) {
                unlockInfo = `<div class="unlock-requirement level-locked">🔒 需要等级 ${stage.unlockLevel} (当前: ${window.Game.player.level})</div>`;
            } else if (stage.id !== 'stage_0' && !hasArmy) {
                unlockInfo = `<div class="unlock-requirement army-locked">⚔️ 需要训练军队</div>`;
            } else if (isCompleted) {
                unlockInfo = `<div class="unlock-requirement completed">🏆 已完成 (重复挑战无奖励)</div>`;
            } else {
                unlockInfo = `<div class="unlock-requirement unlocked">✅ 可以挑战</div>`;
            }

            battleItem.innerHTML = `
                <div class="battle-header">
                    <div class="battle-name">${stage.name}</div>
                    <div class="battle-difficulty difficulty-${stage.difficulty}">${stage.difficulty}</div>
                </div>
                <div class="battle-desc">${stage.description}</div>
                <div class="battle-rewards">奖励: ${this.formatRewards(stage.rewards)}</div>
                <div class="battle-enemies">
                    敌军: ${stage.enemies.map(e => `${e.count}个${GameData.units[e.type]?.name || e.type}`).join(', ')}
                </div>
                ${armyInfo}
                ${unlockInfo}
                <div class="battle-actions">
                    <button class="btn btn-primary" ${canChallenge ? '' : 'disabled'}>挑战</button>
                </div>
            `;

            if (canChallenge) {
                const challengeBtn = battleItem.querySelector('.btn');
                challengeBtn.onclick = () => {
                    this.startBattle(stage.id);
                };
            }

            battleList.appendChild(battleItem);
        }

        container.appendChild(battleList);
    }

    // 开始战斗
    startBattle(stageId) {
        // 特殊处理新手训练关卡
        if (stageId === 'stage_0') {
            // 新手训练不需要军队，使用弹窗模式
            const success = window.Game.battle.startBattle(stageId, []);

            if (success) {
                this.hidePanel('battle');
                // 注意：不调用 showBattleScreen，只使用 SimpleBattleSystem 的弹窗
            }
            return;
        }

        // 获取可用的军队
        const availableUnits = window.Game.army.getAvailableUnits();
        const trainingUnits = window.Game.army.getAllUnits().filter(unit => unit.isTraining);
        const totalUnits = window.Game.army.getAllUnits();

        // 提供更详细的军队状态信息
        if (availableUnits.length === 0) {
            let message = '没有可用的军队！';

            if (totalUnits.length === 0) {
                message += '\n\n💡 建议：\n1. 先建造兵营\n2. 在军队面板训练步兵\n3. 等待训练完成后再来战斗';
            } else if (trainingUnits.length > 0) {
                const trainingInfo = trainingUnits.map(unit =>
                    `${unit.data.name} (${Math.ceil(unit.trainTimeRemaining / 1000)}秒)`
                ).join(', ');
                message += `\n\n⏳ 训练中的军队：${trainingInfo}\n请等待训练完成后再来战斗。`;
            }

            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: message
            });
            return;
        }

        // 显示军队选择界面（未来可以扩展为选择部分军队）
        console.log('可用军队：', availableUnits.map(u => `${u.data.name} (攻击:${u.getAttack()}, 防御:${u.getDefense()}, 生命:${u.health})`));

        // 转换军队数据格式为战斗系统需要的格式
        const battleUnits = availableUnits.map(unit => ({
            id: unit.id,
            type: unit.type,
            name: unit.data.name,
            health: unit.health,
            maxHealth: unit.maxHealth,
            attack: unit.getAttack(),
            defense: unit.getDefense(),
            speed: unit.getSpeed()
        }));

        const success = window.Game.battle.startBattle(stageId, battleUnits);

        if (success) {
            // 隐藏战斗面板，只使用 SimpleBattleSystem 的弹窗
            this.hidePanel('battle');
            // 注意：不调用 showBattleScreen，统一使用 SimpleBattleSystem 的弹窗模式
        }
    }

    // 显示战斗界面
    showBattleScreen(stageId) {
        const battleScreen = Utils.$('#battleScreen');
        const battleTitle = Utils.$('#battleTitle');
        const stageData = GameData.battles.campaign.find(s => s.id === stageId);

        if (battleTitle && stageData) {
            battleTitle.textContent = stageData.name;
        }

        Utils.removeClass(battleScreen, 'hidden');

        // 初始化战斗UI
        this.initBattleUI();

        // 监听战斗事件
        this.setupBattleEventListeners();
    }

    // 隐藏战斗界面
    hideBattleScreen() {
        const battleScreen = Utils.$('#battleScreen');
        Utils.addClass(battleScreen, 'hidden');

        // 清理事件监听
        this.cleanupBattleEventListeners();
    }

    // 初始化战斗UI
    initBattleUI() {
        const battle = window.Game.battle.currentBattle;
        if (!battle) return;

        // 更新回合显示
        this.updateBattleRound(battle.round || 1);

        // 显示双方军队
        this.updateBattleUnits();

        // 清空战斗日志
        const battleLog = Utils.$('#battleLog');
        if (battleLog) {
            battleLog.innerHTML = '<div class="log-entry info">战斗开始！</div>';
        }
    }

    // 更新回合显示
    updateBattleRound(round) {
        const battleRound = Utils.$('#battleRound');
        if (battleRound) {
            battleRound.textContent = `第${round}回合`;
        }
    }

    // 更新战斗单位显示
    updateBattleUnits() {
        const battle = window.Game.battle.currentBattle;
        if (!battle) return;

        // 更新玩家军队
        this.renderBattleUnits('playerUnits', battle.playerUnits, 'player');

        // 更新敌方军队
        this.renderBattleUnits('enemyUnits', battle.enemyUnits, 'enemy');
    }

    // 渲染战斗单位
    renderBattleUnits(containerId, units, side) {
        const container = Utils.$(`#${containerId}`);
        if (!container) return;

        container.innerHTML = '';

        units.forEach(unit => {
            const unitElement = Utils.createElement('div', 'battle-unit');

            if (unit.health <= 0) {
                Utils.addClass(unitElement, 'dead');
            }

            const healthPercent = (unit.health / unit.maxHealth) * 100;

            unitElement.innerHTML = `
                <div class="unit-name">${unit.name || unit.type}</div>
                <div class="unit-health">
                    <span>${unit.health}/${unit.maxHealth}</span>
                    <div class="health-bar">
                        <div class="health-fill" style="width: ${healthPercent}%"></div>
                    </div>
                </div>
                <div class="unit-stats">
                    <span>攻击: ${unit.attack}</span>
                    <span>防御: ${unit.defense}</span>
                    <span>速度: ${unit.speed}</span>
                </div>
            `;

            container.appendChild(unitElement);
        });
    }

    // 添加战斗日志
    addBattleLog(message, type = 'info') {
        const battleLog = Utils.$('#battleLog');
        if (!battleLog) return;

        const logEntry = Utils.createElement('div', `log-entry ${type}`);
        logEntry.textContent = message;

        battleLog.appendChild(logEntry);

        // 自动滚动到底部
        battleLog.scrollTop = battleLog.scrollHeight;

        // 限制日志条数
        const entries = battleLog.querySelectorAll('.log-entry');
        if (entries.length > 50) {
            entries[0].remove();
        }
    }

    // 设置战斗事件监听
    setupBattleEventListeners() {
        // 关闭战斗界面
        const closeBattleBtn = Utils.$('#closeBattleBtn');
        if (closeBattleBtn) {
            closeBattleBtn.onclick = () => {
                this.hideBattleScreen();
            };
        }

        // 战斗控制按钮
        const battleSpeedBtn = Utils.$('#battleSpeedBtn');
        const battleSkipBtn = Utils.$('#battleSkipBtn');
        const battleAutoBtn = Utils.$('#battleAutoBtn');

        if (battleSpeedBtn) {
            battleSpeedBtn.onclick = () => {
                this.toggleBattleSpeed(battleSpeedBtn);
            };
        }

        if (battleSkipBtn) {
            battleSkipBtn.onclick = () => {
                this.skipBattleAnimation();
            };
        }

        if (battleAutoBtn) {
            battleAutoBtn.onclick = () => {
                this.toggleAutoBattle(battleAutoBtn);
            };
        }
    }

    // 清理战斗事件监听
    cleanupBattleEventListeners() {
        const closeBattleBtn = Utils.$('#closeBattleBtn');
        if (closeBattleBtn) {
            closeBattleBtn.onclick = null;
        }
    }

    // 切换战斗速度
    toggleBattleSpeed(button) {
        const speeds = ['1x', '2x', '4x'];
        let currentSpeed = button.textContent.replace('速度', '');
        let currentIndex = speeds.indexOf(currentSpeed);
        
        currentIndex = (currentIndex + 1) % speeds.length;
        const newSpeed = speeds[currentIndex];
        
        button.textContent = newSpeed + '速度';
        
        // 设置战斗系统的速度
        if (window.Game && window.Game.battle) {
            window.Game.battle.setSpeed(parseInt(newSpeed));
        }
        
        this.addBattleLog(`战斗速度设置为 ${newSpeed}`, 'info');
    }

    // 跳过战斗动画
    skipBattleAnimation() {
        if (window.Game && window.Game.battle && window.Game.battle.currentBattle) {
            // 直接计算战斗结果
            window.Game.battle.skipToResult();
            this.addBattleLog('跳过战斗动画，直接显示结果', 'info');
        }
    }

    // 切换自动战斗
    toggleAutoBattle(button) {
        if (window.Game && window.Game.battle) {
            const isAuto = window.Game.battle.isAutoBattle;
            window.Game.battle.setAutoBattle(!isAuto);
            
            button.textContent = !isAuto ? '停止自动' : '自动战斗';
            button.className = !isAuto ? 'btn btn-warning' : 'btn btn-info';
            
            this.addBattleLog(!isAuto ? '开启自动战斗' : '关闭自动战斗', 'info');
        }
    }

    // 格式化奖励
    formatRewards(rewards) {
        if (!rewards) return '';

        const parts = [];
        for (const [resource, amount] of Object.entries(rewards)) {
            if (resource === 'exp') {
                parts.push(`⭐${amount}经验`);
            } else {
                const icon = this.getResourceIcon(resource);
                parts.push(`${icon}${amount}`);
            }
        }
        return parts.join(' ');
    }
}

class Game {
    constructor() {
        this.isInitialized = false;
        this.isRunning = false;
        this.currentScreen = 'loading';

        // 游戏系统
        this.engine = null;
        this.resources = null;
        this.buildings = null;
        this.army = null;
        this.tech = null;
        this.quests = null;
        this.ui = null;

        // 玩家数据
        this.player = {
            level: 1,
            exp: 0,
            expToNext: 100
        };

        this.playtime = 0;
        this.startTime = Date.now();

        this.init();
    }

    // 初始化游戏
    async init() {
        try {
            console.log('Initializing game...');

            // 显示加载进度
            this.updateLoadingProgress(10, '初始化游戏引擎...');
            await Utils.delay(100);

            // 初始化游戏引擎
            this.engine = new GameEngine();
            this.updateLoadingProgress(20, '加载游戏数据...');
            await Utils.delay(100);

            // 初始化游戏系统
            this.resources = new ResourceManager();
            this.updateLoadingProgress(40, '初始化建筑系统...');
            await Utils.delay(100);

            this.buildings = new BuildingSystem();
            this.updateLoadingProgress(50, '初始化军队系统...');
            await Utils.delay(100);

            this.army = new ArmySystem();
            this.updateLoadingProgress(60, '初始化科技系统...');
            await Utils.delay(100);

            this.tech = new TechSystem();
            this.updateLoadingProgress(70, '初始化任务系统...');
            await Utils.delay(100);

            this.quests = new QuestSystem();
            this.updateLoadingProgress(75, '初始化战斗系统...');
            await Utils.delay(100);

            this.battle = new SimpleBattleSystem();
            this.updateLoadingProgress(80, '初始化UI系统...');
            await Utils.delay(100);

            this.ui = new UIManager();
            this.updateLoadingProgress(90, '加载存档系统...');
            await Utils.delay(100);

            // 初始化存档系统
            SaveManager.init();
            this.updateLoadingProgress(90, '完成初始化...');
            await Utils.delay(100);

            // 设置事件监听
            this.setupEventListeners();

            this.updateLoadingProgress(100, '游戏加载完成！');
            await Utils.delay(500);

            this.isInitialized = true;
            this.showMainMenu();

            console.log('Game initialized successfully');

        } catch (error) {
            console.error('Failed to initialize game:', error);
            this.showError('游戏初始化失败，请刷新页面重试。');
        }
    }

    // 更新加载进度
    updateLoadingProgress(percentage, text) {
        const progressBar = Utils.$('#loadingProgress');
        const loadingText = Utils.$('#loadingText');

        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }

        if (loadingText) {
            Utils.setContent(loadingText, text);
        }
    }

    // 显示主菜单
    showMainMenu() {
        this.currentScreen = 'menu';
        Utils.hide('#loadingScreen');
        Utils.show('#menuScreen');

        // 检查是否有存档
        const continueBtn = Utils.$('#continueBtn');
        if (continueBtn) {
            if (SaveManager.hasSaveData()) {
                continueBtn.style.display = 'block';
            } else {
                continueBtn.style.display = 'none';
            }
        }
    }

    // 开始新游戏
    startNewGame() {
        console.log('Starting new game...');

        // 重置游戏数据
        this.resetGameData();

        // 创建新游戏存档
        SaveManager.createNewGame();

        // 开始游戏
        this.startGame();
    }

    // 继续游戏
    continueGame() {
        console.log('Loading saved game...');

        const saveData = SaveManager.loadGame();
        if (saveData) {
            this.loadGameData(saveData);
            this.startGame();
        } else {
            this.showError('无法加载存档，请开始新游戏。');
        }
    }

    // 开始游戏
    startGame() {
        this.currentScreen = 'playing';
        this.isRunning = true;

        Utils.hide('#menuScreen');
        Utils.show('#gameScreen');

        // 等待DOM更新后再设置画布大小
        setTimeout(() => {
            // 确保画布大小正确设置
            this.engine.resizeCanvas();
            console.log('Canvas resized after screen transition');
        }, 100);

        // 启动游戏引擎
        this.engine.start();

        // 激活初始任务
        if (this.quests) {
            this.quests.activateInitialQuests();
        }

        // 更新UI
        this.updateUI();

        // 发送游戏开始事件
        GameEvents.emit(GAME_EVENTS.GAME_START);

        console.log('Game started');
    }

    // 重置游戏数据
    resetGameData() {
        this.player = {
            level: 1,
            exp: 0,
            expToNext: 100
        };

        this.playtime = 0;
        this.startTime = Date.now();

        // 重置各个系统
        if (this.resources) this.resources.reset();
        if (this.buildings) this.buildings.reset();
        if (this.army) this.army.reset();
        if (this.tech) this.tech.reset();
        if (this.quests) this.quests.reset();
        if (this.battle) this.battle.reset();
        
        // 给玩家一些初始军队
        this.giveInitialArmy();
    }
    
    // 给玩家初始军队
    giveInitialArmy() {
        if (!this.army) return;
        
        // 给玩家2个步兵作为初始军队
        for (let i = 0; i < 2; i++) {
            try {
                const unit = new Unit('infantry');
                // 直接添加到军队中，不需要训练时间
                unit.isTraining = false;
                unit.trainTimeRemaining = 0;
                this.army.units.set(unit.id, unit);
            } catch (error) {
                console.error('创建初始军队失败:', error);
            }
        }
        
        console.log('给予玩家初始军队：2个步兵');
    }

    // 加载游戏数据
    loadGameData(data) {
        if (data.player) {
            this.player = { ...data.player };
        }

        if (data.playtime) {
            this.playtime = data.playtime;
        }

        // 加载各系统数据
        if (this.resources && data.resources) {
            this.resources.loadData(data.resources);
        }

        if (this.buildings && data.buildings) {
            this.buildings.loadData(data.buildings);
        }

        if (this.army && data.army) {
            this.army.loadData(data.army);
        }

        if (this.tech && data.technologies) {
            this.tech.loadData(data.technologies);
        }

        if (this.quests && data.quests) {
            this.quests.loadData(data.quests);
        }

        if (this.battle && data.battle) {
            this.battle.loadSaveData(data.battle);
        }
    }

    // 更新UI
    updateUI() {
        // 更新玩家信息
        Utils.setContent('#playerLevel', this.player.level);

        // 更新经验条
        const expProgress = Utils.$('#expProgress');
        if (expProgress) {
            const percentage = (this.player.exp / this.player.expToNext) * 100;
            expProgress.style.width = percentage + '%';
        }

        // 更新人口信息
        const currentPop = this.getCurrentPopulation();
        const maxPop = this.getMaxPopulation();
        Utils.setContent('#populationCurrent', currentPop);
        Utils.setContent('#populationMax', maxPop);
    }

    // 获取当前人口
    getCurrentPopulation() {
        if (this.army) {
            return this.army.getCurrentPopulation();
        }
        return 0;
    }

    // 获取最大人口
    getMaxPopulation() {
        let maxPop = 50; // 基础人口

        // 根据建筑计算人口上限
        if (this.buildings) {
            const buildings = this.buildings.getAllBuildings();
            for (const building of buildings) {
                if (building.type === 'castle') {
                    maxPop += building.level * 20;
                } else if (building.type === 'farm') {
                    maxPop += building.level * 5; // 农场也提供人口
                }
            }
        }

        return maxPop;
    }

    // 保存游戏
    saveGame() {
        if (!this.isRunning) return;

        const success = SaveManager.saveGame();
        if (success) {
            this.ui.showMessage('游戏已保存', MESSAGE_TYPES.SUCCESS);
        } else {
            this.ui.showMessage('保存失败', MESSAGE_TYPES.ERROR);
        }
    }

    // 返回主菜单
    backToMenu() {
        this.isRunning = false;
        this.engine.stop();

        Utils.hide('#gameScreen');
        this.showMainMenu();

        console.log('Returned to main menu');
    }

    // 显示帮助
    showHelp() {
        this.ui.showMessage('游戏帮助：使用鼠标点击建造建筑，管理资源，训练军队，征战四方！', MESSAGE_TYPES.INFO);
    }

    // 显示错误
    showError(message) {
        console.error(message);
        if (this.ui) {
            this.ui.showMessage(message, MESSAGE_TYPES.ERROR);
        } else {
            alert(message);
        }
    }

    // 获得经验
    gainExp(amount) {
        this.player.exp += amount;

        // 检查升级
        while (this.player.exp >= this.player.expToNext) {
            this.levelUp();
        }

        this.updateUI();
        GameEvents.emit(GAME_EVENTS.EXP_GAIN, amount);
    }

    // 升级
    levelUp() {
        this.player.exp -= this.player.expToNext;
        this.player.level++;

        // 计算下一级所需经验
        this.player.expToNext = GameData.levelExp[this.player.level] ||
                                (this.player.expToNext * 1.5);

        this.ui.showMessage(`恭喜升级到${this.player.level}级！`, MESSAGE_TYPES.SUCCESS);
        GameEvents.emit(GAME_EVENTS.LEVEL_UP, this.player.level);
    }

    // 设置事件监听
    setupEventListeners() {
        // 主菜单按钮
        Utils.on('#newGameBtn', 'click', () => this.startNewGame());
        Utils.on('#continueBtn', 'click', () => this.continueGame());
        Utils.on('#helpBtn', 'click', () => this.showHelp());

        // 游戏菜单按钮
        Utils.on('#menuBtn', 'click', () => this.ui.showGameMenu());
        Utils.on('#saveBtn', 'click', () => this.saveGame());
        Utils.on('#loadBtn', 'click', () => this.continueGame());
        Utils.on('#backToMenuBtn', 'click', () => this.backToMenu());
        Utils.on('#resumeBtn', 'click', () => this.ui.hideGameMenu());

        // 操作栏按钮
        Utils.on('#buildBtn', 'click', () => this.ui.togglePanel('building'));
        Utils.on('#armyBtn', 'click', () => this.ui.togglePanel('army'));
        Utils.on('#techBtn', 'click', () => this.ui.togglePanel('tech'));
        Utils.on('#battleBtn', 'click', () => this.ui.togglePanel('battle'));
        Utils.on('#questBtn', 'click', () => this.ui.togglePanel('quest'));

        // 关闭面板按钮 - 使用事件委托
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('close-btn')) {
                e.preventDefault();
                e.stopPropagation();

                const panel = e.target.closest('.side-panel, .modal');
                if (panel) {
                    // 获取面板ID并关闭
                    const panelId = panel.id;
                    if (panelId.endsWith('Panel')) {
                        const panelName = panelId.replace('Panel', '');
                        this.ui.hidePanel(panelName);
                    } else if (panelId === 'gameMenu') {
                        this.ui.hideGameMenu();
                    } else {
                        Utils.hide(panel);
                    }
                }
            }
        });

        // 点击遮罩层关闭面板
        Utils.on('#panelOverlay', 'click', () => {
            if (this.ui && this.ui.currentPanel) {
                this.ui.hidePanel(this.ui.currentPanel);
            }
        });

        // 点击面板外部关闭面板
        document.addEventListener('click', (e) => {
            // 防止在面板内部的点击事件冒泡导致面板关闭
            if (e.target.closest('.side-panel, .modal, .action-btn, .panel-overlay')) {
                return;
            }

            // 如果在建造模式下，不要关闭面板（让用户可以点击画布建造）
            if (window.Game && window.Game.buildings && window.Game.buildings.buildMode) {
                return;
            }

            // 检查是否点击了侧边面板外部
            if (this.ui && this.ui.currentPanel) {
                const panel = Utils.$(`#${this.ui.currentPanel}Panel`);
                const actionBtn = Utils.$(`#${this.ui.currentPanel}Btn`);

                // 如果点击的不是面板内容和对应的按钮，则关闭面板
                if (panel && Utils.hasClass(panel, 'show')) {
                    if (!panel.contains(e.target) &&
                        (!actionBtn || !actionBtn.contains(e.target))) {
                        this.ui.hidePanel(this.ui.currentPanel);
                    }
                }
            }

            // 检查是否点击了建筑信息面板外部
            const buildingInfo = Utils.$('#buildingInfo');
            if (buildingInfo && !Utils.hasClass(buildingInfo, 'hidden')) {
                if (!buildingInfo.contains(e.target)) {
                    Utils.hide(buildingInfo);
                }
            }

            // 检查是否点击了游戏菜单外部
            const gameMenu = Utils.$('#gameMenu');
            if (gameMenu && !Utils.hasClass(gameMenu, 'hidden')) {
                const menuBtn = Utils.$('#menuBtn');
                if (!gameMenu.contains(e.target) &&
                    (!menuBtn || !menuBtn.contains(e.target))) {
                    this.ui.hideGameMenu();
                }
            }
        });

        // 使用事件委托处理按钮点击
        document.addEventListener('click', (e) => {
            // 建筑分类按钮
            if (e.target.classList.contains('category-btn')) {
                // 移除其他按钮的active类
                Utils.$$('.category-btn').forEach(b => Utils.removeClass(b, 'active'));
                // 添加当前按钮的active类
                Utils.addClass(e.target, 'active');
                // 重新加载建筑面板
                if (this.ui) {
                    this.ui.loadBuildingPanel();
                }
            }

            // 标签页按钮
            if (e.target.classList.contains('tab-btn')) {
                const tabContainer = e.target.closest('.army-tabs, .quest-tabs');
                if (tabContainer) {
                    // 移除同组其他按钮的active类
                    tabContainer.querySelectorAll('.tab-btn').forEach(b => Utils.removeClass(b, 'active'));
                    // 添加当前按钮的active类
                    Utils.addClass(e.target, 'active');

                    // 重新加载对应面板
                    if (this.ui) {
                        if (tabContainer.classList.contains('army-tabs')) {
                            this.ui.loadArmyPanel();
                        } else if (tabContainer.classList.contains('quest-tabs')) {
                            this.ui.loadQuestPanel();
                        }
                    }
                }
            }

            // 战斗模式按钮
            if (e.target.classList.contains('mode-btn')) {
                const modeContainer = e.target.closest('.battle-modes');
                if (modeContainer) {
                    // 移除其他按钮的active类
                    modeContainer.querySelectorAll('.mode-btn').forEach(b => Utils.removeClass(b, 'active'));
                    // 添加当前按钮的active类
                    Utils.addClass(e.target, 'active');

                    // 重新加载战斗面板
                    if (this.ui) {
                        this.ui.loadBattlePanel();
                    }
                }
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (this.currentScreen === 'playing') {
                switch (e.code) {
                    case 'Escape':
                        // ESC键优先退出建造模式
                        if (window.Game && window.Game.buildings && window.Game.buildings.buildMode) {
                            window.Game.buildings.exitBuildMode();
                        } else if (this.ui && this.ui.currentPanel) {
                            this.ui.hidePanel(this.ui.currentPanel);
                        } else {
                            this.ui.toggleGameMenu();
                        }
                        break;
                    case 'KeyB':
                        this.ui.togglePanel('building');
                        break;
                    case 'KeyA':
                        this.ui.togglePanel('army');
                        break;
                    case 'KeyT':
                        this.ui.togglePanel('tech');
                        break;
                    case 'KeyF':
                        this.ui.togglePanel('battle');
                        break;
                    case 'KeyQ':
                        this.ui.togglePanel('quest');
                        break;
                }
            }
        });
    }
}

// 启动游戏
window.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, starting game...');
    window.Game = new Game();
});

// 导出
window.UIManager = UIManager;
