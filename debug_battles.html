<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>战斗数据调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #2c3e50;
            color: white;
        }
        .battle-item {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border: 2px solid #3498db;
        }
        .battle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .battle-name {
            font-size: 18px;
            font-weight: bold;
        }
        .battle-difficulty {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: white;
        }
        .difficulty-tutorial { background: #17a2b8; }
        .difficulty-easy { background: #4CAF50; }
        .difficulty-normal { background: #FF9800; }
        .difficulty-hard { background: #F44336; }
    </style>
</head>
<body>
    <h1>战斗关卡数据调试</h1>
    <div id="battleList"></div>

    <script src="js/game/GameData.js"></script>
    <script>
        // 显示战斗数据
        function displayBattleData() {
            const battleList = document.getElementById('battleList');
            const stages = GameData.battles.campaign;

            console.log('Total stages:', stages.length);
            console.log('Stages data:', stages);

            // 检查重复
            const stageIds = stages.map(s => s.id);
            const stageNames = stages.map(s => s.name);
            const duplicateIds = stageIds.filter((id, index) => stageIds.indexOf(id) !== index);
            const duplicateNames = stageNames.filter((name, index) => stageNames.indexOf(name) !== index);

            battleList.innerHTML = `
                <div style="background: rgba(255,255,255,0.1); padding: 10px; margin-bottom: 20px; border-radius: 5px;">
                    <h3>数据分析</h3>
                    <p>总共 ${stages.length} 个关卡</p>
                    <p>重复的ID: ${duplicateIds.length > 0 ? duplicateIds.join(', ') : '无'}</p>
                    <p>重复的名称: ${duplicateNames.length > 0 ? duplicateNames.join(', ') : '无'}</p>
                    <p>所有ID: ${stageIds.join(', ')}</p>
                    <p>所有名称: ${stageNames.join(', ')}</p>
                </div>
            `;

            stages.forEach((stage, index) => {
                console.log(`Stage ${index}:`, stage);

                const battleItem = document.createElement('div');
                battleItem.className = 'battle-item';
                battleItem.innerHTML = `
                    <div class="battle-header">
                        <div class="battle-name">${stage.name}</div>
                        <div class="battle-difficulty difficulty-${stage.difficulty}">${stage.difficulty}</div>
                    </div>
                    <div class="battle-desc">ID: ${stage.id}</div>
                    <div class="battle-desc">${stage.description}</div>
                    <div class="battle-enemies">
                        敌军: ${stage.enemies.map(e => `${e.count}个${e.type}`).join(', ')}
                    </div>
                    <div class="battle-rewards">
                        奖励: ${Object.entries(stage.rewards).map(([k,v]) => `${k}:${v}`).join(', ')}
                    </div>
                    <div class="battle-unlock">解锁等级: ${stage.unlockLevel}</div>
                `;

                battleList.appendChild(battleItem);
            });
        }

        // 页面加载完成后显示数据
        window.addEventListener('load', displayBattleData);
    </script>
</body>
</html>
