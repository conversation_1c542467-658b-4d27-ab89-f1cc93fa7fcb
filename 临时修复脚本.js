// 临时修复脚本 - 直接在浏览器控制台运行
// 这个脚本会直接修改当前加载的 SimpleBattleSystem 实例

console.log('开始应用奖励按钮禁用修复...');

// 备份原始方法
if (window.Game && window.Game.battle) {
    const battleSystem = window.Game.battle;
    
    // 备份原始的 showBattleResult 方法
    battleSystem._originalShowBattleResult = battleSystem.showBattleResult;
    
    // 重写 showBattleResult 方法
    battleSystem.showBattleResult = function(result) {
        const status = document.getElementById('battleStatus');
        const footer = document.querySelector('.battle-dialog-footer');
        const battle = this.currentBattle;

        console.log('=== 战斗结果显示 ===');
        console.log('关卡ID:', battle.stageId);
        console.log('是否胜利:', result.victory);
        console.log('已完成关卡:', Array.from(this.completedStages));

        // 添加最终战斗结果到日志
        if (result.victory) {
            this.addBattleLogMessage('🎉 战斗胜利！敌军全军覆没！', 'victory');
            if (status) {
                status.innerHTML = '🏆 <span style="color: #f39c12;">胜利！</span>';
            }
        } else {
            this.addBattleLogMessage('💀 战斗失败！我军败退...', 'defeat');
            if (status) {
                status.innerHTML = '💀 <span style="color: #e74c3c;">失败！</span>';
            }
        }

        // 显示详细的战斗结果
        if (footer) {
            const rewards = battle.stageData.rewards;
            
            // 检查是否为首次胜利
            const isFirstVictory = result.victory && !this.completedStages.has(battle.stageId);
            console.log('是否首次胜利:', isFirstVictory);
            
            let rewardText = '无奖励';
            if (result.victory) {
                if (isFirstVictory) {
                    rewardText = this.formatRewards(rewards);
                } else {
                    rewardText = '已领取';
                }
            }

            // 计算详细统计
            const totalPlayerUnits = battle.playerUnits.length;
            const totalEnemyUnits = battle.enemyUnits.length;
            const playerSurvivors = totalPlayerUnits - result.playerLosses.length;
            const enemySurvivors = totalEnemyUnits - result.enemyLosses.length;

            // 根据胜负显示不同的按钮
            let buttonsHtml = '';
            if (result.victory) {
                if (isFirstVictory) {
                    console.log('生成首次胜利按钮 - 可领取奖励');
                    buttonsHtml = `
                        <button id="confirmBattleResult" class="btn btn-primary">领取奖励</button>
                        <button id="rechallengeBattle" class="btn btn-secondary">重新挑战</button>
                    `;
                } else {
                    console.log('生成重复胜利按钮 - 奖励已领取，按钮禁用');
                    buttonsHtml = `
                        <button id="confirmBattleResult" class="btn btn-secondary" disabled>奖励已领取</button>
                        <button id="rechallengeBattle" class="btn btn-primary">重新挑战</button>
                    `;
                }
            } else {
                console.log('生成失败按钮');
                buttonsHtml = `
                    <button id="rechallengeBattle" class="btn btn-primary">重新挑战</button>
                    <button id="exitBattle" class="btn btn-secondary">退出</button>
                `;
            }

            footer.innerHTML = `
                <div class="battle-result-detailed">
                    <div class="result-header">
                        <h4>${result.victory ? '🏆 战斗胜利' : '💀 战斗失败'}</h4>
                    </div>
                    <div class="result-stats">
                        <div class="stat-row">
                            <span class="stat-label">战斗评价:</span>
                            <span class="stat-value ${result.victory ? 'victory' : 'defeat'}">
                                ${this.getBattleRating(result)}
                            </span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">我方参战:</span>
                            <span class="stat-value">${totalPlayerUnits} 个单位</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">我方损失:</span>
                            <span class="stat-value ${result.playerLosses.length > 0 ? 'defeat' : 'victory'}">
                                ${result.playerLosses.length} 个单位
                            </span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">我方幸存:</span>
                            <span class="stat-value victory">${playerSurvivors} 个单位</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">敌方损失:</span>
                            <span class="stat-value victory">${result.enemyLosses.length} 个单位</span>
                        </div>
                        ${enemySurvivors > 0 ? `
                        <div class="stat-row">
                            <span class="stat-label">敌方残余:</span>
                            <span class="stat-value defeat">${enemySurvivors} 个单位</span>
                        </div>
                        ` : ''}
                        <div class="stat-row">
                            <span class="stat-label">获得奖励:</span>
                            <span class="stat-value rewards">${rewardText}</span>
                        </div>
                        ${result.victory ? `
                        <div class="stat-row">
                            <span class="stat-label">战斗时长:</span>
                            <span class="stat-value">5 回合</span>
                        </div>
                        ` : ''}
                        ${!result.victory ? '<div class="retry-hint">💡 失败后重新挑战可获得奖励！</div>' : ''}
                    </div>
                </div>
                <div class="battle-result-buttons">
                    ${buttonsHtml}
                </div>
            `;

            // 绑定按钮事件
            const confirmBtn = document.getElementById('confirmBattleResult');
            const rechallengeBtn = document.getElementById('rechallengeBattle');
            const exitBtn = document.getElementById('exitBattle');

            console.log('按钮状态检查:');
            console.log('confirmBtn存在:', !!confirmBtn);
            console.log('confirmBtn禁用状态:', confirmBtn ? confirmBtn.disabled : 'N/A');

            // 只为未禁用的确认按钮绑定事件
            if (confirmBtn && !confirmBtn.disabled) {
                console.log('为确认按钮绑定点击事件');
                confirmBtn.onclick = () => {
                    this.finalizeBattle(result);
                };
            } else {
                console.log('确认按钮被禁用，不绑定点击事件');
            }

            if (rechallengeBtn) {
                rechallengeBtn.onclick = () => {
                    this.rechallengeBattle();
                };
            }

            if (exitBtn) {
                exitBtn.onclick = () => {
                    this.finalizeBattle(result);
                };
            }
        }
    };
    
    console.log('奖励按钮禁用修复已应用！');
    console.log('现在重新挑战已完成的关卡，"领取奖励"按钮应该会被禁用。');
} else {
    console.error('无法找到战斗系统实例');
}
