/* 战斗弹窗样式 */

.battle-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.battle-dialog.hidden {
    opacity: 0;
    pointer-events: none;
}

.battle-dialog-content {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    width: 600px;
    max-width: 90vw;
    max-height: 85vh;
    display: flex;
    flex-direction: column;
    border: 2px solid #3498db;
}

.battle-dialog-header {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #2980b9;
}

.battle-dialog-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.battle-dialog-body {
    padding: 20px;
    color: white;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.battle-armies {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    gap: 20px;
}

.army-section {
    flex: 1;
    text-align: center;
}

.army-section h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #ecf0f1;
}

.player-army h4 {
    color: #3498db;
}

.enemy-army h4 {
    color: #e74c3c;
}

.army-power {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    padding: 8px 12px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
}

.player-army .army-power {
    color: #3498db;
    border: 1px solid #3498db;
}

.enemy-army .army-power {
    color: #e74c3c;
    border: 1px solid #e74c3c;
}

.battle-vs {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: #f39c12;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    min-width: 60px;
}

.army-list {
    text-align: left;
}

.army-unit {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 8px 12px;
    margin-bottom: 8px;
    border-left: 3px solid #3498db;
}

.enemy-army .army-unit {
    border-left-color: #e74c3c;
}

.unit-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.unit-name {
    font-weight: bold;
    font-size: 14px;
}

.unit-stats {
    font-size: 12px;
    color: #bdc3c7;
}

.battle-progress {
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
    border: 1px solid #34495e;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #f39c12 0%, #e67e22 100%);
    width: 0%;
    transition: width 0.1s ease;
    border-radius: 10px;
}

.battle-status {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    color: #ecf0f1;
}

.battle-rewards {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
}

.battle-rewards h4 {
    margin: 0 0 10px 0;
    color: #f39c12;
    font-size: 14px;
}

.rewards-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.reward-item {
    background: rgba(243, 156, 18, 0.2);
    color: #f39c12;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: bold;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.battle-dialog-footer {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    border-top: 1px solid #34495e;
    flex-shrink: 0;
}

.battle-result-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.retry-hint {
    color: #f39c12;
    font-size: 12px;
    text-align: center;
    margin-top: 8px;
    padding: 6px 12px;
    background: rgba(243, 156, 18, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.battle-result-info {
    flex: 1;
    text-align: left;
}

.result-stats {
    color: #bdc3c7;
    font-size: 12px;
}

.result-stats div {
    margin-bottom: 4px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%) !important;
    color: #bdc3c7 !important;
    border: 1px solid #95a5a6 !important;
    transform: none !important;
    box-shadow: none !important;
    pointer-events: none;
}

.btn:disabled:hover {
    background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%) !important;
    transform: none !important;
    box-shadow: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: 1px solid #2980b9;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: white;
    border: 1px solid #7f8c8d;
}

.btn-secondary:hover:not(:disabled) {
    background: linear-gradient(135deg, #7f8c8d 0%, #6c7b7d 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .battle-dialog-content {
        width: 95vw;
        margin: 10px;
    }
    
    .battle-armies {
        flex-direction: column;
        gap: 15px;
    }
    
    .battle-vs {
        transform: rotate(90deg);
        margin: 10px 0;
    }
    
    .battle-dialog-footer {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn {
        width: 100%;
    }
}

/* 胜率显示样式 */
.win-chance-display {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.win-chance-header {
    text-align: center;
    font-weight: bold;
    color: #ecf0f1;
    margin-bottom: 10px;
    font-size: 14px;
}

.win-chance-bar {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    height: 8px;
    margin: 8px 0;
    overflow: hidden;
}

.win-chance-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
}

.win-chance-text {
    text-align: center;
    font-weight: bold;
    font-size: 13px;
    margin: 8px 0;
}

.battle-tips {
    text-align: center;
    font-size: 12px;
    color: #bdc3c7;
    font-style: italic;
}

/* 战斗日志样式 */
.battle-log-container {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    margin: 15px 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-height: 200px;
    overflow: hidden;
}

.battle-log-header {
    background: rgba(52, 152, 219, 0.2);
    padding: 8px 12px;
    font-weight: bold;
    color: #3498db;
    font-size: 14px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.battle-log-content {
    padding: 10px;
    max-height: 150px;
    overflow-y: auto;
    font-size: 12px;
}

.battle-log-message {
    margin: 4px 0;
    padding: 4px 8px;
    border-radius: 4px;
    line-height: 1.4;
}

.battle-log-message.round-start {
    background: rgba(241, 196, 15, 0.2);
    color: #f1c40f;
    font-weight: bold;
    text-align: center;
}

.battle-log-message.player-attack {
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
}

.battle-log-message.enemy-attack {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.battle-log-message.special-event {
    background: rgba(155, 89, 182, 0.2);
    color: #9b59b6;
    font-weight: bold;
}

.battle-log-message.victory {
    background: rgba(39, 174, 96, 0.3);
    color: #27ae60;
    font-weight: bold;
    text-align: center;
}

.battle-log-message.defeat {
    background: rgba(231, 76, 60, 0.3);
    color: #e74c3c;
    font-weight: bold;
    text-align: center;
}

/* 战斗结果详细信息样式 */
.battle-result-detailed {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.result-header h4 {
    margin: 0 0 15px 0;
    text-align: center;
    font-size: 18px;
}

.result-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-label {
    color: #bdc3c7;
    font-size: 14px;
}

.stat-value {
    color: #ecf0f1;
    font-weight: bold;
    font-size: 14px;
}

.stat-value.victory {
    color: #27ae60;
}

.stat-value.defeat {
    color: #e74c3c;
}

.stat-value.rewards {
    color: #f39c12;
}

/* 动画效果 */
@keyframes battlePulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.battle-vs {
    animation: battlePulse 2s infinite;
}

.progress-fill {
    position: relative;
    overflow: hidden;
}

/* 滚动条样式 */
.battle-log-content::-webkit-scrollbar,
.battle-dialog-body::-webkit-scrollbar {
    width: 6px;
}

.battle-log-content::-webkit-scrollbar-track,
.battle-dialog-body::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.battle-log-content::-webkit-scrollbar-thumb,
.battle-dialog-body::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.battle-log-content::-webkit-scrollbar-thumb:hover,
.battle-dialog-body::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShine 1.5s infinite;
}

@keyframes progressShine {
    0% { left: -100%; }
    100% { left: 100%; }
}
