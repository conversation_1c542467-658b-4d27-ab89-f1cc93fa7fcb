/* 王国争霸H5 - 主样式文件 */

/* 基础重置和全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 通用隐藏类 */
.hidden {
    display: none !important;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

#gameContainer {
    width: 100vw;
    height: 100vh;
    position: relative;
    overflow: hidden;
}

/* 屏幕切换 */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: opacity 0.5s ease-in-out;
}

.screen.hidden {
    opacity: 0;
    pointer-events: none;
}

/* 加载界面 */
#loadingScreen {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.loading-content {
    text-align: center;
}

.loading-content h1 {
    font-size: 3rem;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.loading-bar {
    width: 300px;
    height: 20px;
    background: rgba(255,255,255,0.2);
    border-radius: 10px;
    overflow: hidden;
    margin: 1rem auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

#loadingText {
    font-size: 1.2rem;
    margin-top: 1rem;
}

/* 主菜单界面 */
#menuScreen {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-content {
    text-align: center;
    color: white;
}

.game-title {
    font-size: 4rem;
    margin-bottom: 3rem;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
    background: linear-gradient(45deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.menu-btn {
    padding: 1rem 2rem;
    font-size: 1.5rem;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    min-width: 200px;
}

.menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.4);
    background: linear-gradient(45deg, #45a049, #4CAF50);
}

.menu-btn:active {
    transform: translateY(0);
}

/* 游戏主界面 */
#gameScreen {
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

/* 顶部信息栏 */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 10px 20px;
    height: 80px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.resource-bar {
    display: flex;
    gap: 20px;
}

.resource-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255,255,255,0.1);
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: bold;
}

.resource-icon {
    font-size: 1.5rem;
}

.player-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: flex-end;
}

.level-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.exp-bar {
    width: 150px;
    height: 10px;
    background: rgba(255,255,255,0.2);
    border-radius: 5px;
    overflow: hidden;
}

.exp-progress {
    height: 100%;
    background: linear-gradient(90deg, #FFD700, #FFA500);
    width: 30%;
    transition: width 0.3s ease;
}

/* 游戏区域 */
.game-area {
    flex: 1;
    position: relative;
    overflow: hidden;
}

#gameCanvas {
    display: block;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
    cursor: pointer;
}

/* 建筑信息面板 */
.info-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0,0,0,0.9);
    color: white;
    padding: 20px;
    border-radius: 10px;
    max-width: 300px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.5);
}

.info-panel h4 {
    margin-bottom: 10px;
    color: #FFD700;
}

.building-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 底部操作栏 */
.action-bar {
    display: flex;
    justify-content: center;
    gap: 10px;
    background: rgba(0,0,0,0.8);
    padding: 15px;
    height: 100px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 10px 15px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
    font-size: 0.9rem;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    background: linear-gradient(45deg, #764ba2, #667eea);
}

.action-btn.active {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
    transform: translateY(-1px);
}

.btn-icon {
    font-size: 1.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-title {
        font-size: 2.5rem;
        margin-bottom: 2rem;
    }
    
    .menu-btn {
        font-size: 1.2rem;
        padding: 0.8rem 1.5rem;
        min-width: 180px;
    }
    
    .top-bar {
        padding: 8px 15px;
        height: 70px;
    }
    
    .resource-bar {
        gap: 10px;
    }
    
    .resource-item {
        padding: 6px 10px;
        font-size: 0.9rem;
    }
    
    .action-bar {
        padding: 10px;
        height: 80px;
    }
    
    .action-btn {
        min-width: 60px;
        font-size: 0.8rem;
        padding: 8px 10px;
    }
    
    .btn-icon {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .resource-bar {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .resource-item {
        padding: 4px 8px;
        font-size: 0.8rem;
    }
    
    .resource-icon {
        font-size: 1.2rem;
    }
    
    .action-bar {
        gap: 5px;
    }
    
    .action-btn {
        min-width: 50px;
        font-size: 0.7rem;
        padding: 6px 8px;
    }
}
