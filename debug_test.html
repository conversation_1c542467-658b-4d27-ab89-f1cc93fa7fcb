<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>建造功能调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .error { color: #ff6b6b; }
        .warning { color: #ffa500; }
        .info { color: #87ceeb; }
        .success { color: #90ee90; }
        
        #gameFrame {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <h1>🔧 王国争霸H5 - 建造功能调试测试</h1>
    
    <div class="test-container">
        <h2>🎮 游戏测试</h2>
        <p>在下面的iframe中测试游戏，同时观察右侧的日志输出。</p>
        <iframe id="gameFrame" src="http://localhost:8000"></iframe>
    </div>
    
    <div class="test-container">
        <h2>🔍 调试控制</h2>
        <button class="test-button" onclick="clearLog()">清空日志</button>
        <button class="test-button" onclick="testGameState()">检查游戏状态</button>
        <button class="test-button" onclick="testBuildMode()">手动进入建造模式</button>
        <button class="test-button" onclick="testCanvasClick()">模拟画布点击</button>
        <button class="test-button" onclick="exportLog()">导出日志</button>
    </div>
    
    <div class="test-container">
        <h2>📋 实时日志</h2>
        <div id="logContainer" class="log-container">
            <div class="info">调试日志将在这里显示...</div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>📝 测试步骤</h2>
        <ol>
            <li>确保游戏服务器在 http://localhost:8000 运行</li>
            <li>在上方iframe中开始新游戏</li>
            <li>点击"建造"按钮</li>
            <li>点击"农场"建筑</li>
            <li>观察日志输出，查看是否有错误</li>
            <li>尝试在画布上点击建造</li>
        </ol>
    </div>

    <script>
        let logContainer = document.getElementById('logContainer');
        let logCount = 0;
        
        // 拦截console输出
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };
        
        function addLog(message, type = 'info') {
            logCount++;
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 限制日志数量
            if (logContainer.children.length > 100) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }
        
        // 重写console方法
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addLog(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addLog('ERROR: ' + args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addLog('WARN: ' + args.join(' '), 'warning');
        };
        
        console.info = function(...args) {
            originalConsole.info.apply(console, args);
            addLog('INFO: ' + args.join(' '), 'info');
        };
        
        // 拦截iframe中的console输出
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'console') {
                addLog(`[IFRAME] ${event.data.level.toUpperCase()}: ${event.data.message}`, event.data.level);
            }
        });
        
        function clearLog() {
            logContainer.innerHTML = '<div class="info">日志已清空...</div>';
            logCount = 0;
        }
        
        function testGameState() {
            addLog('=== 检查游戏状态 ===', 'info');
            try {
                const gameFrame = document.getElementById('gameFrame');
                const gameWindow = gameFrame.contentWindow;
                
                if (gameWindow.Game) {
                    addLog('✓ Game对象存在', 'success');
                    addLog(`✓ 游戏状态: ${gameWindow.Game.currentScreen}`, 'info');
                    
                    if (gameWindow.Game.buildings) {
                        addLog('✓ BuildingSystem存在', 'success');
                        addLog(`✓ 建造模式: ${gameWindow.Game.buildings.buildMode}`, 'info');
                        addLog(`✓ 建筑类型: ${gameWindow.Game.buildings.buildingType}`, 'info');
                    } else {
                        addLog('✗ BuildingSystem不存在', 'error');
                    }
                    
                    if (gameWindow.Game.engine) {
                        addLog('✓ GameEngine存在', 'success');
                        addLog(`✓ 引擎状态: ${gameWindow.Game.engine.gameState}`, 'info');
                    } else {
                        addLog('✗ GameEngine不存在', 'error');
                    }
                } else {
                    addLog('✗ Game对象不存在', 'error');
                }
            } catch (error) {
                addLog(`检查游戏状态时出错: ${error.message}`, 'error');
            }
        }
        
        function testBuildMode() {
            addLog('=== 手动进入建造模式 ===', 'info');
            try {
                const gameFrame = document.getElementById('gameFrame');
                const gameWindow = gameFrame.contentWindow;
                
                if (gameWindow.Game && gameWindow.Game.buildings) {
                    gameWindow.Game.buildings.enterBuildMode('farm');
                    addLog('✓ 手动调用enterBuildMode(farm)', 'success');
                    
                    setTimeout(() => {
                        addLog(`建造模式状态: ${gameWindow.Game.buildings.buildMode}`, 'info');
                        addLog(`建筑类型: ${gameWindow.Game.buildings.buildingType}`, 'info');
                    }, 100);
                } else {
                    addLog('✗ 无法访问建筑系统', 'error');
                }
            } catch (error) {
                addLog(`手动进入建造模式时出错: ${error.message}`, 'error');
            }
        }
        
        function testCanvasClick() {
            addLog('=== 模拟画布点击 ===', 'info');
            try {
                const gameFrame = document.getElementById('gameFrame');
                const gameWindow = gameFrame.contentWindow;
                
                if (gameWindow.Game && gameWindow.Game.engine) {
                    // 模拟点击画布中心
                    const canvas = gameWindow.Game.engine.canvas;
                    if (canvas) {
                        const rect = canvas.getBoundingClientRect();
                        const x = rect.width / 2;
                        const y = rect.height / 2;
                        
                        addLog(`模拟点击位置: (${x}, ${y})`, 'info');
                        gameWindow.Game.engine.handleClick(x, y);
                        addLog('✓ 模拟点击已发送', 'success');
                    } else {
                        addLog('✗ 无法找到画布元素', 'error');
                    }
                } else {
                    addLog('✗ 无法访问游戏引擎', 'error');
                }
            } catch (error) {
                addLog(`模拟画布点击时出错: ${error.message}`, 'error');
            }
        }
        
        function exportLog() {
            const logs = Array.from(logContainer.children).map(child => child.textContent).join('\n');
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `debug_log_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            addLog('✓ 日志已导出', 'success');
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            addLog('调试页面已加载', 'success');
            addLog('请在上方iframe中测试游戏功能', 'info');
        });
        
        // 监听iframe加载
        document.getElementById('gameFrame').addEventListener('load', function() {
            addLog('游戏iframe已加载', 'success');
        });
    </script>
</body>
</html>
